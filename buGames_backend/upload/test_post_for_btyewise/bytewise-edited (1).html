<!DOCTYPE html>
<html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ByteWise - Customizable AI Chatbots for Education</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer=""></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0EA5E9',
                        'primary-dark': '#0284C7',
                        'primary-light': '#38BDF8',
                        'tech-dark': '#0283dd',
                        'tech-electric': '#0EA5E9',
                        'tech-cyan': '#06e9c1',
                        'tech-blue': '#0F172A',
                        'tech-accent': '#00D4FF',
                        'bg-light': '#FFFFFF',
                        'bg-dark': '#0F172A',
                        'text-light': '#1F2937',
                        'text-dark': '#F9FAFB'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700&family=Work+Sans:wght@100;200;300;400;500;600;700&family=Montserrat:wght@100;200;300;400;500;600;700&family=Raleway:wght@100;200;300;400;500;600;700&family=Lato:wght@100;300;400;700&family=Source+Sans+Pro:wght@200;300;400;600;700&family=Nunito+Sans:wght@200;300;400;600;700&family=Playfair+Display:wght@300;400;500;600;700&family=Cormorant+Garamond:wght@300;400;500;600;700&family=Crimson+Text:wght@300;400;600&family=Libre+Baskerville:wght@300;400;700&family=Lora:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&family=Georgia&family=Cardo:wght@400;700&display=swap');
        body { font-family: 'Work Sans', sans-serif; }
        
        .font-poppins { font-family: 'Poppins', sans-serif; font-weight: 200; letter-spacing: -0.02em; }
        .font-montserrat { font-family: 'Montserrat', sans-serif; font-weight: 200; letter-spacing: -0.01em; }
        .font-raleway { font-family: 'Raleway', sans-serif; font-weight: 200; letter-spacing: 0.01em; }
        .font-lato { font-family: 'Lato', sans-serif; font-weight: 300; letter-spacing: -0.01em; }
        .font-source { font-family: 'Source Sans Pro', sans-serif; font-weight: 200; letter-spacing: 0.01em; }
        .font-nunito { font-family: 'Nunito Sans', sans-serif; font-weight: 200; letter-spacing: -0.01em; }
        
        .elegant-font { font-family: 'Playfair Display', serif; font-weight: 400; letter-spacing: -0.01em; }
        .gradient-bg { background: linear-gradient(135deg, #0EA5E9 0%, #06B6D4 50%, #00D4FF 100%); }
        .tech-gradient-bg { background: linear-gradient(135deg, #0F172A 0%, #1E40AF 50%, #0EA5E9 100%); }
        .smooth-tech-gradient {
            background: linear-gradient(90deg, #1E40AF 0%, #3B82F6 20%, #6366F1 40%, #8B5CF6 60%, #A855F7 80%, #EC4899 100%);
            background-clip: text; -webkit-background-clip: text; -webkit-text-fill-color: transparent;
        }
        
        .gradient-border { position: relative; background: white; border: 2px solid transparent; background-clip: padding-box; }
        .gradient-border::before {
            content: ''; position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: -1; margin: -2px;
            border-radius: inherit; background: linear-gradient(90deg, #1E40AF 0%, #3B82F6 20%, #6366F1 40%, #8B5CF6 60%, #A855F7 80%, #EC4899 100%);
        }
        
        .dark .gradient-border { background: #1F2937; }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-5px); box-shadow: 0 20px 40px rgba(14, 165, 233, 0.15); }
        .animate-float { animation: float 6s ease-in-out infinite; }
        @keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-20px); } }
        .dark .bg-light { background-color: #181818; }
        .dark .text-light { color: #F9FAFB; }
        
        .editable-section { position: relative; }
        .editable-section:hover .edit-controls { opacity: 1; }
        .edit-controls { 
            opacity: 0; 
            transition: opacity 0.3s ease;
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }
        .editing { outline: 2px dashed #0EA5E9; outline-offset: 4px; }
        [contenteditable="true"] { min-height: 20px; }
        [contenteditable="true"]:focus { outline: 2px solid #0EA5E9; outline-offset: 2px; background: rgba(14, 165, 233, 0.05); }
        
        .download-btn {
            position: fixed;
            top: 80px;
            left: 110px;
            z-index: 1000;
            background: #0EA5E9;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
            transition: all 0.3s ease;
        }
        .download-btn:hover {
            background: #0284C7;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(14, 165, 233, 0.4);
        }
    </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.relative{position:relative}.sticky{position:sticky}.top-0{top:0px}.z-50{z-index:50}.mx-auto{margin-left:auto;margin-right:auto}.mb-16{margin-bottom:4rem}.mb-2{margin-bottom:0.5rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.ml-2{margin-left:0.5rem}.mt-1{margin-top:0.25rem}.mt-4{margin-top:1rem}.inline-block{display:inline-block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.h-10{height:2.5rem}.h-12{height:3rem}.h-16{height:4rem}.h-20{height:5rem}.h-8{height:2rem}.min-h-screen{min-height:100vh}.w-10{width:2.5rem}.w-12{width:3rem}.w-16{width:4rem}.w-20{width:5rem}.w-8{width:2rem}.max-w-2xl{max-width:42rem}.max-w-3xl{max-width:48rem}.flex-shrink-0{flex-shrink:0}.transform{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.flex-col{flex-direction:column}.items-start{align-items:flex-start}.items-center{align-items:center}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-12{gap:3rem}.gap-4{gap:1rem}.gap-8{gap:2rem}.space-x-2 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.5rem * var(--tw-space-x-reverse));margin-left:calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-3 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.75rem * var(--tw-space-x-reverse));margin-left:calc(0.75rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-8 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(2rem * var(--tw-space-x-reverse));margin-left:calc(2rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-2 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.5rem * var(--tw-space-y-reverse))}.space-y-4 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.space-y-6 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1.5rem * var(--tw-space-y-reverse))}.space-y-8 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(2rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(2rem * var(--tw-space-y-reverse))}.rounded{border-radius:0.25rem}.rounded-2xl{border-radius:1rem}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:0.5rem}.rounded-xl{border-radius:0.75rem}.border{border-width:1px}.border-2{border-width:2px}.border-b{border-bottom-width:1px}.border-t{border-top-width:1px}.border-gray-200{--tw-border-opacity:1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1))}.border-gray-800{--tw-border-opacity:1;border-color:rgb(31 41 55 / var(--tw-border-opacity, 1))}.border-primary{--tw-border-opacity:1;border-color:rgb(14 165 233 / var(--tw-border-opacity, 1))}.border-white{--tw-border-opacity:1;border-color:rgb(255 255 255 / var(--tw-border-opacity, 1))}.bg-blue-50{--tw-bg-opacity:1;background-color:rgb(239 246 255 / var(--tw-bg-opacity, 1))}.bg-gray-100{--tw-bg-opacity:1;background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1))}.bg-gray-50{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1))}.bg-gray-900{--tw-bg-opacity:1;background-color:rgb(17 24 39 / var(--tw-bg-opacity, 1))}.bg-primary{--tw-bg-opacity:1;background-color:rgb(14 165 233 / var(--tw-bg-opacity, 1))}.bg-primary\/10{background-color:rgb(14 165 233 / 0.1)}.bg-primary\/5{background-color:rgb(14 165 233 / 0.05)}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-white\/95{background-color:rgb(255 255 255 / 0.95)}.bg-gradient-to-br{background-image:linear-gradient(to bottom right, var(--tw-gradient-stops))}.from-blue-50{--tw-gradient-from:#eff6ff var(--tw-gradient-from-position);--tw-gradient-to:rgb(239 246 255 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.to-cyan-50{--tw-gradient-to:#ecfeff var(--tw-gradient-to-position)}.p-2{padding:0.5rem}.p-4{padding:1rem}.p-6{padding:1.5rem}.p-8{padding:2rem}.px-3{padding-left:0.75rem;padding-right:0.75rem}.px-4{padding-left:1rem;padding-right:1rem}.px-8{padding-left:2rem;padding-right:2rem}.py-1{padding-top:0.25rem;padding-bottom:0.25rem}.py-16{padding-top:4rem;padding-bottom:4rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.py-20{padding-top:5rem;padding-bottom:5rem}.py-4{padding-top:1rem;padding-bottom:1rem}.pb-4{padding-bottom:1rem}.pt-4{padding-top:1rem}.pt-8{padding-top:2rem}.text-center{text-align:center}.text-2xl{font-size:1.5rem;line-height:2rem}.text-4xl{font-size:2.25rem;line-height:2.5rem}.text-5xl{font-size:3rem;line-height:1}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:0.75rem;line-height:1rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.font-semibold{font-weight:600}.leading-relaxed{line-height:1.625}.leading-tight{line-height:1.25}.text-blue-800{--tw-text-opacity:1;color:rgb(30 64 175 / var(--tw-text-opacity, 1))}.text-gray-300{--tw-text-opacity:1;color:rgb(209 213 219 / var(--tw-text-opacity, 1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.text-gray-900{--tw-text-opacity:1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.text-green-500{--tw-text-opacity:1;color:rgb(34 197 94 / var(--tw-text-opacity, 1))}.text-primary{--tw-text-opacity:1;color:rgb(14 165 233 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.text-white\/90{color:rgb(255 255 255 / 0.9)}.shadow-2xl{--tw-shadow:0 25px 50px -12px rgb(0 0 0 / 0.25);--tw-shadow-colored:0 25px 50px -12px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-xl{--tw-shadow:0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.backdrop-blur-sm{--tw-backdrop-blur:blur(4px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.transition-colors{transition-property:color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.duration-300{transition-duration:300ms}.hover\:scale-105:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.hover\:bg-gray-100:hover{--tw-bg-opacity:1;background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1))}.hover\:bg-primary:hover{--tw-bg-opacity:1;background-color:rgb(14 165 233 / var(--tw-bg-opacity, 1))}.hover\:bg-primary-dark:hover{--tw-bg-opacity:1;background-color:rgb(2 132 199 / var(--tw-bg-opacity, 1))}.hover\:bg-white:hover{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.hover\:text-primary:hover{--tw-text-opacity:1;color:rgb(14 165 233 / var(--tw-text-opacity, 1))}.hover\:text-white:hover{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}@media (min-width: 640px){.sm\:flex-row{flex-direction:row}}@media (min-width: 768px){.md\:mt-0{margin-top:0px}.md\:flex{display:flex}.md\:hidden{display:none}.md\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.md\:grid-cols-4{grid-template-columns:repeat(4, minmax(0, 1fr))}.md\:flex-row{flex-direction:row}}@media (min-width: 1024px){.lg\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.lg\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.lg\:py-32{padding-top:8rem;padding-bottom:8rem}.lg\:text-5xl{font-size:3rem;line-height:1}.lg\:text-6xl{font-size:3.75rem;line-height:1}}@media (prefers-color-scheme: dark){.dark\:border-gray-700{--tw-border-opacity:1;border-color:rgb(55 65 81 / var(--tw-border-opacity, 1))}.dark\:bg-blue-900\/20{background-color:rgb(30 58 138 / 0.2)}.dark\:bg-gray-700{--tw-bg-opacity:1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1))}.dark\:bg-gray-800{--tw-bg-opacity:1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1))}.dark\:bg-gray-900{--tw-bg-opacity:1;background-color:rgb(17 24 39 / var(--tw-bg-opacity, 1))}.dark\:bg-gray-900\/95{background-color:rgb(17 24 39 / 0.95)}.dark\:from-slate-900{--tw-gradient-from:#0f172a var(--tw-gradient-from-position);--tw-gradient-to:rgb(15 23 42 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.dark\:to-blue-900{--tw-gradient-to:#1e3a8a var(--tw-gradient-to-position)}.dark\:text-blue-200{--tw-text-opacity:1;color:rgb(191 219 254 / var(--tw-text-opacity, 1))}.dark\:text-gray-100{--tw-text-opacity:1;color:rgb(243 244 246 / var(--tw-text-opacity, 1))}.dark\:text-gray-300{--tw-text-opacity:1;color:rgb(209 213 219 / var(--tw-text-opacity, 1))}}</style><style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 2147483647;
  display: flex;
  justify-content: center;
  align-items: center;
}
.immersive-translate-attach-loading::after {
  content: " ";

  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-2000%, -50%);
  z-index: 100;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.immersive-translate-input-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;
}

@keyframes immersiveTranslateShadowRolling {
  0% {
    box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  12% {
    box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
      100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
  }

  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
      110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }

  62% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
      120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }

  75% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }

  87% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
  }

  100% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
  }
}

.immersive-translate-toast {
  display: flex;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  right: 0;
  top: 1%;
  width: fit-content;
  padding: 12px 20px;
  margin: auto;
  overflow: auto;
  background: #fef6f9;
  box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
  font-size: 15px;
  border-radius: 8px;
  color: #333;
}

.immersive-translate-toast-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.immersive-translate-toast-hidden {
  margin: 0 20px 0 72px;
  text-decoration: underline;
  cursor: pointer;
}

.immersive-translate-toast-close {
  color: #666666;
  font-size: 20px;
  font-weight: bold;
  padding: 0 10px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .immersive-translate-toast {
    top: 0;
    padding: 12px 0px 0 10px;
  }
  .immersive-translate-toast-content {
    flex-direction: column;
    text-align: center;
  }
  .immersive-translate-toast-hidden {
    margin: 10px auto;
  }
}

.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 15px;
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 10% auto;
  padding: 40px 24px 24px;
  border-radius: 12px;
  width: 350px;
  font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  position: relative;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-content {
    margin: 50% auto !important;
  }
}

.immersive-translate-modal .immersive-translate-modal-content-in-input {
  max-width: 500px;
}
.immersive-translate-modal-content-in-input .immersive-translate-modal-body {
  text-align: left;
  max-height: unset;
}

.immersive-translate-modal-title {
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
}

.immersive-translate-modal-body {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  margin-top: 24px;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-body {
    max-height: 250px;
    overflow-y: auto;
  }
}

.immersive-translate-close {
  color: #666666;
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 20px;
  font-weight: bold;
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-footer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.immersive-translate-btn {
  width: fit-content;
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 14px;
  margin: 0 8px;
  padding: 9px 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.immersive-translate-btn:hover {
  background-color: #f082ac;
}
.immersive-translate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.immersive-translate-btn:disabled:hover {
  background-color: #ea4c89;
}

.immersive-translate-cancel-btn {
  /* gray color */
  background-color: rgb(89, 107, 120);
}

.immersive-translate-cancel-btn:hover {
  background-color: hsl(205, 20%, 32%);
}

.immersive-translate-action-btn {
  background-color: transparent;
  color: #ea4c89;
  border: 1px solid #ea4c89;
}

.immersive-translate-btn svg {
  margin-right: 5px;
}

.immersive-translate-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #007bff;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-primary-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-modal input[type="radio"] {
  margin: 0 6px;
  cursor: pointer;
}

.immersive-translate-modal label {
  cursor: pointer;
}

.immersive-translate-close-action {
  position: absolute;
  top: 2px;
  right: 0px;
  cursor: pointer;
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 16px !important;
}
.imt-image-status img,
.imt-image-status svg,
.imt-img-loading {
  width: 28px !important;
  height: 28px !important;
  margin: 0 0 8px 0 !important;
  min-height: 28px !important;
  min-width: 28px !important;
  position: relative !important;
}
.imt-img-loading {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
  background-size: 28px 28px;
  animation: image-loading-rotate 1s linear infinite !important;
}

.imt-image-status span {
  color: var(--bg-2, #fff) !important;
  font-size: 14px !important;
  line-height: 14px !important;
  font-weight: 500 !important;
  font-family: "PingFang SC", Arial, sans-serif !important;
}

@keyframes image-loading-rotate {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
</style></head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300" data-new-gr-c-s-check-loaded="14.1242.0" data-gr-ext-installed="">
    <div x-data="{ 
        darkMode: false, 
        mobileMenu: false,
        editMode: false,
        savedSections: {},
        init() {
            this.darkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
            this.$watch('darkMode', value =&gt; {
                if (value) {
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                }
            });
            // Enable double-click editing
            this.enableDoubleClickEdit();
        },
        enableDoubleClickEdit() {
            // Add double-click listeners to all editable elements
            document.addEventListener('dblclick', (e) =&gt; {
                const target = e.target.closest('[data-editable]');
                if (target) {
                    e.preventDefault(); // Prevent default behavior
                    
                    // Don't edit if already editing
                    if (target.querySelector('input') || target.contentEditable === 'true') return;
                    
                    // Store the original content and styles
                    const originalTextContent = target.textContent.trim();
                    const targetRect = target.getBoundingClientRect();
                    const computedStyle = window.getComputedStyle(target);
                    
                    // Store original styles for gradient text
                    const originalBackground = target.style.background;
                    const originalWebkitBackground = target.style.webkitBackgroundClip;
                    const originalBackgroundClip = target.style.backgroundClip;
                    const originalWebkitTextFill = target.style.webkitTextFillColor;
                    const originalColor = target.style.color;
                    
                    // Store original href if it's a link
                    const isLink = target.tagName.toLowerCase() === 'a';
                    const originalHref = isLink ? target.getAttribute('href') : null;
                    
                    // Check if element has gradient text class
                    const hasGradient = target.classList.contains('smooth-tech-gradient');
                    
                    // Create an input element for editing
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.value = originalTextContent;
                    input.style.cssText = `
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        border: 2px solid #0EA5E9;
                        border-radius: 4px;
                        background: rgba(14, 165, 233, 0.05);
                        color: ${hasGradient ? '#6366F1' : computedStyle.color};
                        font-family: ${computedStyle.fontFamily};
                        font-size: ${computedStyle.fontSize};
                        font-weight: ${computedStyle.fontWeight};
                        text-align: ${computedStyle.textAlign};
                        padding: 4px 8px;
                        margin: 0;
                        outline: none;
                        z-index: 1000;
                        box-sizing: border-box;
                    `;
                    
                    // Position the target relatively and hide text temporarily
                    const originalPosition = target.style.position;
                    const originalVisibility = target.style.visibility;
                    target.style.position = 'relative';
                    target.style.color = 'transparent';
                    
                    // Temporarily remove gradient effects
                    if (hasGradient) {
                        target.style.background = 'none';
                        target.style.webkitBackgroundClip = 'initial';
                        target.style.backgroundClip = 'initial';
                        target.style.webkitTextFillColor = 'initial';
                    }
                    
                    // For links, temporarily remove href
                    if (isLink) {
                        target.removeAttribute('href');
                    }
                    
                    // Insert input and focus
                    target.appendChild(input);
                    input.focus();
                    input.select();
                    
                    // Function to exit edit mode
                    const exitEdit = (save = true) =&gt; {
                        if (save) {
                            target.textContent = input.value;
                        }
                        
                        // Remove input
                        if (input.parentNode) {
                            input.parentNode.removeChild(input);
                        }
                        
                        // Restore original styles and position
                        target.style.position = originalPosition;
                        target.style.visibility = originalVisibility;
                        target.style.color = originalColor;
                        
                        // Restore gradient styles if it was a gradient element
                        if (hasGradient) {
                            target.style.background = originalBackground;
                            target.style.webkitBackgroundClip = originalWebkitBackground;
                            target.style.backgroundClip = originalBackgroundClip;
                            target.style.webkitTextFillColor = originalWebkitTextFill;
                        }
                        
                        // Restore href for links
                        if (isLink &amp;&amp; originalHref) {
                            target.href = originalHref;
                        }
                        
                        // Remove event listeners
                        input.removeEventListener('blur', handleBlur);
                        input.removeEventListener('keydown', handleKeydown);
                    };
                    
                    // Handle blur event
                    const handleBlur = () =&gt; {
                        exitEdit(true);
                    };
                    
                    // Handle keydown events
                    const handleKeydown = (e) =&gt; {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            exitEdit(true);
                        } else if (e.key === 'Escape') {
                            e.preventDefault();
                            exitEdit(false);
                        }
                        // All other keys including space work normally
                    };
                    
                    // Add event listeners
                    input.addEventListener('blur', handleBlur);
                    input.addEventListener('keydown', handleKeydown);
                }
            });
        },
        saveSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                this.savedSections[sectionId] = section.innerHTML;
                // Show success feedback
                const saveBtn = section.querySelector('.save-btn');
                if (saveBtn) {
                    const originalText = saveBtn.innerHTML;
                    saveBtn.innerHTML = '&lt;i class=&quot;fas fa-check&quot;&gt;&lt;/i&gt; Saved!';
                    saveBtn.style.background = '#10B981';
                    setTimeout(() =&gt; {
                        saveBtn.innerHTML = originalText;
                        saveBtn.style.background = '#0EA5E9';
                    }, 2000);
                }
            }
        },
        downloadHTML() {
            // Create a copy of the document
            const docClone = document.documentElement.cloneNode(true);
            
            // Remove edit controls and reset contenteditable
            const editControls = docClone.querySelectorAll('.edit-controls, .download-btn, .edit-mode-toggle');
            editControls.forEach(el =&gt; el.remove());
            
            const editableSections = docClone.querySelectorAll('.editable-section');
            editableSections.forEach(section =&gt; {
                section.classList.remove('editing');
                const editableElements = section.querySelectorAll('[data-editable]');
                editableElements.forEach(el =&gt; {
                    el.removeAttribute('contenteditable');
                });
            });
            
            // Create download
            const htmlContent = '&lt;!DOCTYPE html&gt;\n' + docClone.outerHTML;
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'bytewise-edited.html';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    }" class="min-h-screen">

        <!-- Fixed Controls -->
        

        <!-- Header -->
        <header id="header-section" class="editable-section sticky top-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
            
            <nav class="container mx-auto px-4 py-4">
                <div class="flex items-center justify-between">
                    <!-- Logo -->
                    <div class="flex items-center space-x-2">
                        <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-robot text-white text-lg"></i>
                        </div>
                        <span class="text-2xl font-bold smooth-tech-gradient" data-editable="">ByteWise</span>
                    </div>

                    <!-- Desktop Navigation -->
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="#home" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Home</a>
                        <a href="#students" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Students</a>
                        <a href="#teachers" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Teachers</a>
                        <a href="#community" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Community</a>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="hidden md:flex items-center space-x-4">
                        <button class="px-4 py-2 text-primary gradient-border rounded-lg hover:bg-primary hover:text-white transition-all" data-editable="">
                            Login
                        </button>
                        <button class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-all" data-editable="">
                            Register
                        </button>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button @click="mobileMenu = !mobileMenu" class="md:hidden p-2">
                        <i class="fas fa-bars text-gray-600 dark:text-gray-300"></i>
                    </button>
                </div>

                <!-- Mobile Menu -->
                <div x-show="mobileMenu" x-transition="" class="md:hidden mt-4 pb-4 border-t border-gray-200 dark:border-gray-700 pt-4" style="display: none;">
                    <div class="flex flex-col space-y-4">
                        <a href="#home" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Home</a>
                        <a href="#students" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Students</a>
                        <a href="#teachers" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Teachers</a>
                        <a href="#community" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Community</a>
                        <div class="flex flex-col space-y-2 pt-4">
                            <button class="px-4 py-2 text-primary border border-primary rounded-lg hover:bg-primary hover:text-white transition-all" data-editable="">
                                Login
                            </button>
                            <button class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-all" data-editable="">
                                Register
                            </button>
                        </div>
                    </div>
                </div>
            </nav>
        </header>

        <!-- Hero Section -->
        <section id="hero-section" class="editable-section py-20 lg:py-32 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-slate-900 dark:to-blue-900">
            
            <div class="container mx-auto px-4">
                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <div class="space-y-8">
                        <div class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium" data-editable="">
                            AI for Educators
                        </div>
                        <h1 class="text-5xl lg:text-6xl leading-tight elegant-font">
                            <span class="smooth-tech-gradient" data-editable="">
                                Customizable AI Chatbots for Education
                            </span>
                        </h1>
                        <p class="text-xl text-gray-600 dark:text-gray-300 leading-relaxed" data-editable="">
                            Empower students and educators with intelligent, personalized learning experiences powered by cutting-edge AI technology. Our adaptive solutions analyze performance data to adjust lesson plans, ensuring each learner is challenged and supported at the optimal level for maximum engagement and retention.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button class="px-8 py-4 bg-primary text-white rounded-xl hover:bg-primary-dark transition-all transform hover:scale-105 font-medium text-lg" data-editable="">
                                Explore ByteWise Now
                                <i class="fas fa-arrow-right ml-2"></i>
                            </button>
                            <button class="px-8 py-4 text-gray-700 dark:text-gray-300 hover:text-primary transition-all font-medium text-lg" data-editable="">
                                Learn more
                            </button>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 animate-float">
                            <div class="space-y-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                        <i class="fas fa-robot text-white text-sm"></i>
                                    </div>
                                    <span class="font-medium" data-editable="">AI Assistant</span>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <p class="text-sm" data-editable="">Hi! I'm your personalized learning assistant. How can I help you with your studies today?</p>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="px-3 py-2 bg-primary/10 text-primary rounded-lg text-sm" data-editable="">Explain Concepts</button>
                                    <button class="px-3 py-2 bg-primary/10 text-primary rounded-lg text-sm" data-editable="">Practice Quiz</button>
                                    <button class="px-3 py-2 bg-primary/10 text-primary rounded-lg text-sm" data-editable="">Study Plan</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Overview -->
        <section id="features-section" class="editable-section py-20 bg-white dark:bg-gray-900">
            
            <div class="container mx-auto px-4">
                <div class="grid md:grid-cols-4 gap-8">
                    <div class="text-center space-y-4 card-hover p-6 rounded-xl">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                            <i class="fas fa-graduation-cap text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg elegant-font" data-editable="">Personalized Learning</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">AI adapts to each student's learning style and pace</p>
                    </div>
                    
                    <div class="text-center space-y-4 card-hover p-6 rounded-xl">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                            <i class="fas fa-chalkboard-teacher text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg elegant-font" data-editable="">Teacher Tools</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">Comprehensive dashboard for course management</p>
                    </div>
                    
                    <div class="text-center space-y-4 card-hover p-6 rounded-xl">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                            <i class="fas fa-comments text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg elegant-font" data-editable="">Interactive Chat</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">Natural conversations with AI tutors</p>
                    </div>
                    
                    <div class="text-center space-y-4 card-hover p-6 rounded-xl">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                            <i class="fas fa-chart-line text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg elegant-font" data-editable="">Analytics</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">Track progress and identify learning gaps</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- For Students Section -->
        <section id="students-section" class="editable-section py-20 bg-gray-50 dark:bg-gray-800">
            
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl lg:text-5xl mb-6 elegant-font">
                        <span class="smooth-tech-gradient" data-editable="">AI for Student Success</span>
                    </h2>
                    <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto" data-editable="">
                        Get personalized help, instant feedback, and adaptive learning experiences that grow with you.
                    </p>
                </div>

                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <div class="space-y-8">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
                                <span class="text-white font-bold">1</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg mb-2 elegant-font" data-editable="">Ask Questions Anytime</h3>
                                <p class="text-gray-600 dark:text-gray-300" data-editable="">Get instant answers to your questions, whether it's homework help or concept clarification.</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
                                <span class="text-white font-bold">2</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg mb-2 elegant-font" data-editable="">Personalized Study Plans</h3>
                                <p class="text-gray-600 dark:text-gray-300" data-editable="">AI creates custom study schedules based on your learning goals and availability.</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
                                <span class="text-white font-bold">3</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg mb-2 elegant-font" data-editable="">Track Your Progress</h3>
                                <p class="text-gray-600 dark:text-gray-300" data-editable="">Monitor your learning journey with detailed analytics and achievement badges.</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-xl p-8">
                        <div class="space-y-6">
                            <div class="flex items-center justify-between">
                                <h3 class="font-semibold" data-editable="">Study Session</h3>
                                <span class="text-sm text-gray-500" data-editable="">2:30 PM</span>
                            </div>
                            
                            <div class="space-y-4">
                                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                                    <p class="text-sm font-medium text-blue-800 dark:text-blue-200" data-editable="">Student</p>
                                    <p class="text-sm mt-1" data-editable="">Can you explain photosynthesis in simple terms?</p>
                                </div>
                                
                                <div class="bg-primary/10 rounded-lg p-4">
                                    <p class="text-sm font-medium text-primary" data-editable="">AI Tutor</p>
                                    <p class="text-sm mt-1" data-editable="">Photosynthesis is like a plant's way of making food using sunlight, water, and carbon dioxide. Think of it as nature's solar panel! Would you like me to break it down into steps?</p>
                                </div>
                                
                                <div class="flex space-x-2">
                                    <button class="px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg text-sm" data-editable="">Yes, show steps</button>
                                    <button class="px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg text-sm" data-editable="">Give example</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- For Teachers Section -->
        <section id="teachers-section" class="editable-section py-20 bg-white dark:bg-gray-900">
            
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl lg:text-5xl mb-6 elegant-font">
                        <span class="smooth-tech-gradient" data-editable="">Empower Teachers with AI</span>
                    </h2>
                    <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto" data-editable="">
                        Create custom chatbots, automate administrative tasks, and gain insights into student performance.
                    </p>
                </div>

                <div class="grid lg:grid-cols-3 gap-8">
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 card-hover">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-robot text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-xl mb-4 elegant-font" data-editable="">Custom Chatbots</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-6" data-editable="">Design AI assistants tailored to your curriculum and teaching style.</p>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Subject-specific knowledge</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Custom personality</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Integration with materials</span>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 card-hover">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-chart-bar text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-xl mb-4 elegant-font" data-editable="">Student Analytics</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-6" data-editable="">Get detailed insights into student engagement and learning patterns.</p>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Performance tracking</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Engagement metrics</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Learning gap identification</span>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 card-hover">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-cogs text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-xl mb-4 elegant-font" data-editable="">Automation Tools</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-6" data-editable="">Automate repetitive tasks and focus on what matters most - teaching.</p>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Auto-grading</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">FAQ responses</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Progress reports</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Community Section -->
        <section id="community-section" class="editable-section py-20 bg-primary/5 dark:bg-gray-800">
            
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl lg:text-5xl mb-6 elegant-font">
                        <span class="smooth-tech-gradient" data-editable="">Join the Community</span>
                    </h2>
                    <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto" data-editable="">
                        Connect with educators and students from Hong Kong and beyond who are transforming education with AI.
                    </p>
                </div>

                <div class="grid md:grid-cols-3 gap-8 mb-16">
                    <div class="text-center">
                        <div class="text-4xl font-bold text-primary mb-2" data-editable="">10,000+</div>
                        <p class="text-gray-600 dark:text-gray-300" data-editable="">Active Users</p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-primary mb-2" data-editable="">500+</div>
                        <p class="text-gray-600 dark:text-gray-300" data-editable="">Educational Institutions</p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-primary mb-2" data-editable="">50+</div>
                        <p class="text-gray-600 dark:text-gray-300" data-editable="">Subject Areas</p>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-xl">
                    <div class="grid md:grid-cols-3 gap-8">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-university text-primary text-2xl"></i>
                            </div>
                            <h3 class="font-semibold text-lg mb-2" data-editable="">University of Hong Kong</h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">"ByteWise has revolutionized our computer science curriculum with personalized AI tutoring."</p>
                        </div>
                        
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-school text-primary text-2xl"></i>
                            </div>
                            <h3 class="font-semibold text-lg mb-2" data-editable="">HKUST</h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">"Our students love the 24/7 availability of AI tutors for engineering problem-solving."</p>
                        </div>
                        
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-graduation-cap text-primary text-2xl"></i>
                            </div>
                            <h3 class="font-semibold text-lg mb-2" data-editable="">CUHK</h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">"The analytics help us identify struggling students early and provide targeted support."</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section id="cta-section" class="editable-section py-20 gradient-bg">
            
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-4xl lg:text-5xl text-white mb-6 elegant-font" data-editable="">
                    Ready to Transform Education?
                </h2>
                <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto" data-editable="">
                    Join thousands of educators and students who are already using ByteWise to create better learning experiences.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="px-8 py-4 bg-white text-primary rounded-xl hover:bg-gray-100 transition-all font-medium text-lg" data-editable="">
                        Start Free Trial
                    </button>
                    <button class="px-8 py-4 border-2 border-white text-white rounded-xl hover:bg-white hover:text-primary transition-all font-medium text-lg" data-editable="">
                        Schedule Demo
                    </button>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer id="footer-section" class="editable-section py-16 bg-gray-900 text-gray-300">
            
            <div class="container mx-auto px-4">
                <div class="grid md:grid-cols-4 gap-8 mb-8">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                                <i class="fas fa-robot text-white"></i>
                            </div>
                            <span class="text-xl font-bold text-white" data-editable="">ByteWise</span>
                        </div>
                        <p class="text-sm" data-editable="">Transforming education with AI-powered chatbots for personalized learning experiences.</p>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-white mb-4 elegant-font" data-editable="">Product</h4>
                        <ul class="space-y-2 text-sm">
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Features</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Pricing</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">API</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Integrations</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-white mb-4 elegant-font" data-editable="">Resources</h4>
                        <ul class="space-y-2 text-sm">
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Documentation</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Blog</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Help Center</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Community</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-white mb-4 elegant-font" data-editable="">Company</h4>
                        <ul class="space-y-2 text-sm">
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">About</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Careers</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Privacy</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Terms</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
                    <p class="text-sm" data-editable="">© 2024 ByteWise. All rights reserved.</p>
                    <div class="flex space-x-4 mt-4 md:mt-0">
                        <a href="#" class="hover:text-primary transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="hover:text-primary transition-colors">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="hover:text-primary transition-colors">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Dark mode detection
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const href = this.getAttribute('href');
                
                // Check if href is valid and not just '#'
                if (href && href.length > 1 && href !== '#') {
                    try {
                        const target = document.querySelector(href);
                        if (target) {
                            target.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    } catch (error) {
                        console.warn('Invalid selector:', href);
                        // If selector is invalid, just ignore the error
                    }
                }
            });
        });
    </script>



</body><div id="immersive-translate-popup" style="all: initial"></div><grammarly-desktop-integration data-grammarly-shadow-root="true"></grammarly-desktop-integration></html>