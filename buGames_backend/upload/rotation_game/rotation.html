<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图形旋转演示</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .controls {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
            animation: fadeIn 0.5s ease-out;
            width: 100%;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background-color: #fff;
            transition: all 0.3s ease;
            min-width: 200px;
            flex: 1;
        }
        .control-group:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
            width: 100%;
        }
        button {
            padding: 12px 24px;
            cursor: pointer;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            flex: 1;
            min-width: 120px;
            touch-action: manipulation;
        }
        button:hover {
            background-color: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        button:active {
            transform: translateY(0);
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        #demo-area {
            position: relative;
            width: 100%;
            max-width: 400px;
            height: 400px;
            margin: 20px auto;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            overflow: visible;
            background-color: #fff;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        #demo-area:hover {
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
        #original-shape {
            position: absolute;
            opacity: 0.5;
            transition: all 0.3s ease;
        }
        #rotating-shape {
            position: absolute;
            transition: transform 0.05s linear;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }
        .pivot-point {
            position: absolute;
            width: 12px;
            height: 12px;
            background-color: #2ecc71;
            border-radius: 50%;
            z-index: 10;
            transform: translate(-6px, -6px);
            cursor: move;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            touch-action: none;
        }
        .pivot-point:hover, .pivot-point:active {
            transform: translate(-6px, -6px) scale(1.2);
            background-color: #27ae60;
        }
        .pivot-point.touching {
            transform: translate(-6px, -6px) scale(1.3);
            background-color: #27ae60;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        .info-panel {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            animation: slideUp 0.5s ease-out;
            width: 100%;
            box-sizing: border-box;
        }
        select {
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            background-color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
            width: 100%;
            touch-action: manipulation;
        }
        select:hover {
            border-color: #4CAF50;
        }
        .trail-point {
            position: absolute;
            width: 4px;
            height: 4px;
            background-color: rgba(46, 204, 113, 0.6);
            border-radius: 50%;
            transform: translate(-2px, -2px);
            animation: fadeIn 0.3s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .demo-screen {
            display: none;
            animation: fadeIn 0.5s ease-out;
        }
        .selection-screen {
            display: block;
            animation: fadeIn 0.5s ease-out;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2em;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }
        h2 {
            color: #34495e;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        h3 {
            color: #7f8c8d;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        .triangle {
            width: 0;
            height: 0;
            border-left: 50px solid transparent;
            border-right: 50px solid transparent;
            border-bottom: 100px solid #3498db;
            left: 150px;
            top: 100px;
        }
        .diamond {
            width: 120px;
            height: 80px;
            background-color: #3498db;
            left: 140px;
            top: 160px;
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
        }
        .trapezoid {
            width: 160px;
            height: 0;
            border-bottom: 100px solid #3498db;
            border-left: 40px solid transparent;
            border-right: 40px solid transparent;
            left: 100px;
            top: 150px;
        }
        #drag-hint {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
            display: none;
        }
        
        /* 移动设备适配 */
        /* @media screen and (max-width: 768px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 15px;
            }
            .controls {
                gap: 10px;
            }
            .control-group {
                min-width: 150px;
                padding: 10px;
            }
            .buttons {
                flex-direction: column;
            }
            button {
                padding: 15px 20px;
                font-size: 18px;
                min-width: 140px;
            }
            select {
                padding: 15px;
                font-size: 18px;
            }
            #demo-area {
                height: 300px;
            }
            h1 {
                font-size: 1.8em;
                margin-bottom: 20px;
            }
            h2 {
                font-size: 1.3em;
            }
            h3 {
                font-size: 1.1em;
            }
            .info-panel {
                padding: 15px;
                font-size: 14px;
            }
            .pivot-point {
                width: 16px;
                height: 16px;
                transform: translate(-8px, -8px);
            }
            .pivot-point:hover, .pivot-point:active, .pivot-point.touching {
                transform: translate(-8px, -8px) scale(1.2);
            }
        } */

        /* 小屏幕设备适配 */
        /* @media screen and (max-width: 480px) {
            .control-group {
                min-width: 100%;
            }
            .buttons {
                flex-direction: column;
            }
            button {
                width: 100%;
            }
            #demo-area {
                height: 250px;
            }
            h1 {
                font-size: 1.5em;
            }
            .info-panel {
                font-size: 13px;
            }
        } */
    </style>
</head>
<body>
    <div class="container">
        <h1>图形旋转演示</h1>
        
        <div id="selection-screen" class="selection-screen">
            <h2>请选择旋转参数</h2>
            <div class="controls">
                <div class="control-group">
                    <h3>选择图形</h3>
                    <select id="shape-select">
                        <option value="triangle">三角形</option>
                        <option value="diamond">菱形</option>
                        <option value="trapezoid">梯形</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <h3>选择方向</h3>
                    <select id="direction-select">
                        <option value="clockwise">顺时针</option>
                        <option value="counterclockwise">逆时针</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <h3>选择角度</h3>
                    <select id="angle-select">
                        <option value="90">90度</option>
                        <option value="180">180度</option>
                        <option value="270">270度</option>
                        <option value="360">360度</option>
                    </select>
                </div>

                <div class="control-group">
                    <h3>旋转中心位置</h3>
                    <select id="pivot-select">
                        <option value="center">图形中心</option>
                        <option value="vertex">图形顶点</option>
                        <option value="bottom-center">底部中心</option>
                        <option value="custom">自定义（拖动绿点）</option>
                    </select>
                </div>
            </div>
            
            <button id="start-demo">开始演示</button>
            
            
        </div>
        
        <div id="demo-screen" class="demo-screen">
            <h2>旋转演示</h2>
            <div id="demo-area">
                <div id="original-shape" class="triangle"></div>
                <div id="rotating-shape" class="triangle" style="background-color: #e74c3c; border-bottom-color: #e74c3c;"></div>
                <div id="pivot-point" class="pivot-point"></div>
                <div id="trail-path"></div>
            </div>
            
            <div class="buttons">
                <button id="play-btn">播放</button>
                <button id="pause-btn">暂停</button>
                <button id="reset-btn">重置</button>
                <button id="back-btn">返回</button>
            </div>
            
            <div class="info-panel">
                <p>当前旋转角度: <span id="current-angle">0</span>°</p>
                <p>旋转方向: <span id="current-direction">顺时针</span></p>
                <p>目标角度: <span id="target-angle">90</span>°</p>
                <p>旋转中心: <span id="pivot-position">(200, 200)</span></p>
                <p id="drag-hint">提示：选择"自定义"后，可以拖动红点改变旋转中心</p>
            </div>
        </div>
    </div>
    <div class="info-panel">
        <p>本演示由香港浸会大学语文中心 Simon Wang 老师利用 DeepSeek 为河南新乡封丘县陈桥镇三合头小学制作  感谢苗苗老师提供思路和意见</p >
        <p style="margin: 15px 0;">我们利用豆包制作了可以语音聊天的智能体，可以配合这个游戏使用：</p >
        <a href="https://doubao.com/bot/weYQ9Qgv" class="" target="_blank">点击进入语音聊天智能体</a >
    </div>

    <script>
        // 获取DOM元素
        const selectionScreen = document.getElementById('selection-screen');
        const demoScreen = document.getElementById('demo-screen');
        const startDemoBtn = document.getElementById('start-demo');
        const backBtn = document.getElementById('back-btn');
        const playBtn = document.getElementById('play-btn');
        const pauseBtn = document.getElementById('pause-btn');
        const resetBtn = document.getElementById('reset-btn');
        const shapeSelect = document.getElementById('shape-select');
        const directionSelect = document.getElementById('direction-select');
        const angleSelect = document.getElementById('angle-select');
        const pivotSelect = document.getElementById('pivot-select');
        const originalShape = document.getElementById('original-shape');
        const rotatingShape = document.getElementById('rotating-shape');
        const pivotPoint = document.getElementById('pivot-point');
        const trailPath = document.getElementById('trail-path');
        const currentAngleDisplay = document.getElementById('current-angle');
        const currentDirectionDisplay = document.getElementById('current-direction');
        const targetAngleDisplay = document.getElementById('target-angle');
        const pivotPositionDisplay = document.getElementById('pivot-position');
        const dragHint = document.getElementById('drag-hint');
        
        // 旋转相关变量
        let currentAngle = 0;
        let targetAngle = 0;
        let rotationInterval;
        let isPlaying = false;
        let rotationDirection = 1;
        let pivotPosition = { x: 200, y: 200 };
        let shapeType = 'triangle';
        let isDragging = false;
        let startDragPos = { x: 0, y: 0 };
        let trailPoints = [];

        // 图形尺寸和位置信息
        const shapeInfo = {
            triangle: { width: 100, height: 100, left: 150, top: 100, color: '#3498db' },
            diamond: { width: 120, height: 80, left: 140, top: 160, color: '#3498db' },
            trapezoid: { width: 160, height: 100, left: 80, top: 150, color: '#3498db' }
        };

        // 设置形状
        function setShape(shape) {
            shapeType = shape;
            const info = shapeInfo[shape];
            
            // 清除之前的类和样式
            originalShape.className = '';
            rotatingShape.className = '';
            originalShape.style = '';
            rotatingShape.style = '';
            
            // 设置新形状
            originalShape.classList.add(shape);
            rotatingShape.classList.add(shape);
            
            // 设置颜色
            if (shape === 'triangle') {
                originalShape.style.borderBottomColor = info.color;
                rotatingShape.style.borderBottomColor = '#e74c3c'; // 红色三角形
            }else if(shape === 'trapezoid'){
                originalShape.style.borderBottomColor = info.color;
                originalShape.style.borderLeftColor = 'transparent';
                originalShape.style.borderRightColor = 'transparent';
                rotatingShape.style.borderBottomColor = '#e74c3c';
                rotatingShape.style.borderLeftColor = 'transparent';
                rotatingShape.style.borderRightColor = 'transparent';
            }else{
                originalShape.style.backgroundColor = info.color;
                rotatingShape.style.backgroundColor = '#e74c3c'; // 红色其他图形
            }
            
            // 设置位置
            originalShape.style.left = `${info.left}px`;
            originalShape.style.top = `${info.top}px`;
            rotatingShape.style.left = `${info.left}px`;
            rotatingShape.style.top = `${info.top}px`;
            
            // 清除轨迹
            clearTrail();
            
            // 设置旋转中心
            setPivotPosition(pivotSelect.value);
        }

        // 设置旋转中心位置
        function setPivotPosition(position) {
            const info = shapeInfo[shapeType];
            let centerX, centerY;
            
            switch(shapeType) {
                case 'triangle':
                    centerX = info.left + 50;  // 三角形底边中点
                    centerY = info.top + 33;   // 三角形高度1/3处（重心）
                    break;
                case 'diamond':
                    centerX = info.left + info.width / 2;
                    centerY = info.top + info.height / 2;
                    break;
                case 'trapezoid':
                    centerX = info.left + info.width / 2 + 40;  // 梯形底边中点
                    centerY = info.top + info.height / 2;  // 梯形高度中点
                    break;
            }
            
            // 设置旋转中心点
            switch(position) {
                case 'center':
                    if (shapeType === 'triangle') {
                        // 三角形的重心在高度1/3处
                        pivotPosition = {
                            x: centerX,
                            y: info.top + 66  // 三角形高度的1/3处
                        };
                    } else if (shapeType === 'trapezoid') {
                        // 梯形的中心点在高度中点
                        pivotPosition = {
                            x: centerX,
                            y: info.top + 50  // 梯形高度的一半
                        };
                    } else {
                        pivotPosition = { x: centerX, y: centerY };
                    }
                    break;
                case 'vertex':
                    if (shapeType === 'triangle') {
                        // 三角形的顶点在顶部
                        pivotPosition = {
                            x: centerX,
                            y: info.top
                        };
                    } else if (shapeType === 'trapezoid') {
                        // 梯形的顶点在顶部中点
                        pivotPosition = {
                            x: centerX,
                            y: info.top
                        };
                    } else {
                        pivotPosition = {
                            x: centerX,
                            y: shapeType === 'diamond' ? info.top : info.top + info.height
                        };
                    }
                    break;
                case 'bottom-center':
                    if (shapeType === 'trapezoid') {
                        // 梯形的底部中心
                        pivotPosition = {
                            x: centerX,
                            y: info.top + 100  // 梯形高度
                        };
                    } else {
                        pivotPosition = {
                            x: centerX,
                            y: info.top + info.height
                        };
                    }
                    break;
                case 'custom':
                    // 保持当前自定义位置不变，如果未设置则使用中心点
                    pivotPosition = pivotPosition || { x: centerX, y: centerY };
                    break;
                default:
                    // 默认使用中心点
                    pivotPosition = { x: centerX, y: centerY };
            }
            
            updatePivotPoint();
            
            // 如果是自定义模式，显示拖动提示
            dragHint.style.display = position === 'custom' ? 'block' : 'none';
        }

        // 更新旋转中心点位置
        function updatePivotPoint() {
            pivotPoint.style.left = `${pivotPosition.x}px`;
            pivotPoint.style.top = `${pivotPosition.y}px`;
            pivotPositionDisplay.textContent = `(${Math.round(pivotPosition.x)}, ${Math.round(pivotPosition.y)})`;
            
            // 更新旋转中心
            const info = shapeInfo[shapeType];
            const originX = pivotPosition.x - info.left;
            const originY = pivotPosition.y - info.top;
            rotatingShape.style.transformOrigin = `${originX}px ${originY}px`;
        }

        // 清除轨迹
        function clearTrail() {
            trailPath.innerHTML = '';
            trailPoints = [];
        }

        // 添加轨迹点
        function addTrailPoint() {
            const trailPoint = document.createElement('div');
            trailPoint.className = 'trail-point';
            
            // 获取旋转图形的中心点
            const rect = rotatingShape.getBoundingClientRect();
            const demoRect = document.getElementById('demo-area').getBoundingClientRect();
            const centerX = rect.left + rect.width/2 - demoRect.left;
            const centerY = rect.top + rect.height/2 - demoRect.top;
            
            trailPoint.style.left = `${centerX}px`;
            trailPoint.style.top = `${centerY}px`;
            
            trailPath.appendChild(trailPoint);
            trailPoints.push({x: centerX, y: centerY});
        }

        // 开始演示
        startDemoBtn.addEventListener('click', () => {
            setShape(shapeSelect.value);
            
            rotationDirection = directionSelect.value === 'clockwise' ? 1 : -1;
            currentDirectionDisplay.textContent = directionSelect.value === 'clockwise' ? '顺时针' : '逆时针';
            
            targetAngle = parseInt(angleSelect.value);
            targetAngleDisplay.textContent = targetAngle;
            
            setPivotPosition(pivotSelect.value);
            
            currentAngle = 0;
            currentAngleDisplay.textContent = '0';
            rotatingShape.style.transform = 'rotate(0deg)';
            
            selectionScreen.style.display = 'none';
            demoScreen.style.display = 'block';
            
            if (rotationInterval) {
                clearInterval(rotationInterval);
                isPlaying = false;
            }
        });
        
        // 返回选择界面
        backBtn.addEventListener('click', () => {
            selectionScreen.style.display = 'block';
            demoScreen.style.display = 'none';
            
            if (rotationInterval) {
                clearInterval(rotationInterval);
                isPlaying = false;
            }
        });
        
        // 播放旋转
        playBtn.addEventListener('click', () => {
            if (!isPlaying) {
                isPlaying = true;
                rotationInterval = setInterval(() => {
                    currentAngle += rotationDirection;
                    const displayAngle = (currentAngle % 360 + 360) % 360;
                    currentAngleDisplay.textContent = displayAngle;
                    
                    rotatingShape.style.transform = `rotate(${currentAngle}deg)`;
                    
                    // 添加轨迹点
                    if (Math.abs(currentAngle) % 10 === 0) {
                        addTrailPoint();
                    }
                    
                    if (Math.abs(currentAngle) >= targetAngle) {
                        clearInterval(rotationInterval);
                        isPlaying = false;
                    }
                }, 50);
            }
        });
        
        // 暂停旋转
        pauseBtn.addEventListener('click', () => {
            if (isPlaying) {
                clearInterval(rotationInterval);
                isPlaying = false;
            }
        });
        
        // 重置旋转
        resetBtn.addEventListener('click', () => {
            if (rotationInterval) {
                clearInterval(rotationInterval);
                isPlaying = false;
            }
            
            currentAngle = 0;
            currentAngleDisplay.textContent = '0';
            rotatingShape.style.transform = 'rotate(0deg)';
            clearTrail();
        });

        // 旋转中心选择变化
        pivotSelect.addEventListener('change', (e) => {
            if (demoScreen.style.display === 'block') {
                setPivotPosition(e.target.value);
                rotatingShape.style.transform = `rotate(${currentAngle}deg)`;
            }
        });

        // 拖动旋转中心点的功能
        pivotPoint.addEventListener('mousedown', (e) => {
            if (pivotSelect.value === 'custom') {
                isDragging = true;
                const rect = document.getElementById('demo-area').getBoundingClientRect();
                startDragPos = {
                    x: e.clientX - rect.left - pivotPosition.x,
                    y: e.clientY - rect.top - pivotPosition.y
                };
                e.preventDefault();
            }
        });

        // 添加触摸事件支持
        pivotPoint.addEventListener('touchstart', (e) => {
            if (pivotSelect.value === 'custom') {
                isDragging = true;
                const touch = e.touches[0];
                const rect = document.getElementById('demo-area').getBoundingClientRect();
                startDragPos = {
                    x: touch.clientX - rect.left - pivotPosition.x,
                    y: touch.clientY - rect.top - pivotPosition.y
                };
                pivotPoint.classList.add('touching');
                e.preventDefault();
            }
        }, { passive: false });

        document.addEventListener('mousemove', (e) => {
            if (isDragging && pivotSelect.value === 'custom') {
                const rect = document.getElementById('demo-area').getBoundingClientRect();
                // 计算鼠标相对于演示区域的位置
                const relativeX = e.clientX - rect.left;
                const relativeY = e.clientY - rect.top;
                // 限制拖动范围在演示区域内
                pivotPosition.x = Math.max(0, Math.min(rect.width, relativeX - startDragPos.x));
                pivotPosition.y = Math.max(0, Math.min(rect.height, relativeY - startDragPos.y));
                updatePivotPoint();
                rotatingShape.style.transform = `rotate(${currentAngle}deg)`;
            }
        });

        // 添加触摸移动事件
        document.addEventListener('touchmove', (e) => {
            if (isDragging && pivotSelect.value === 'custom') {
                const touch = e.touches[0];
                const rect = document.getElementById('demo-area').getBoundingClientRect();
                // 计算触摸点相对于演示区域的位置
                const relativeX = touch.clientX - rect.left;
                const relativeY = touch.clientY - rect.top;
                // 限制拖动范围在演示区域内
                pivotPosition.x = Math.max(0, Math.min(rect.width, relativeX - startDragPos.x));
                pivotPosition.y = Math.max(0, Math.min(rect.height, relativeY - startDragPos.y));
                updatePivotPoint();
                rotatingShape.style.transform = `rotate(${currentAngle}deg)`;
                e.preventDefault();
            }
        }, { passive: false });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                pivotPoint.classList.remove('touching');
            }
        });

        // 添加触摸结束事件
        document.addEventListener('touchend', () => {
            if (isDragging) {
                isDragging = false;
                pivotPoint.classList.remove('touching');
            }
        }, { passive: true });

        // 防止拖动时选中文本
        document.addEventListener('selectstart', (e) => {
            if (isDragging) {
                e.preventDefault();
            }
        });

        // 添加触摸取消事件
        document.addEventListener('touchcancel', () => {
            if (isDragging) {
                isDragging = false;
                pivotPoint.classList.remove('touching');
            }
        }, { passive: true });

        // 防止页面缩放
        document.addEventListener('gesturestart', (e) => {
            e.preventDefault();
        }, { passive: false });
    </script>
</body>
</html>