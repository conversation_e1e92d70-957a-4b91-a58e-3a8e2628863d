<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="Content-Style-Type" content="text/css">
  <title></title>
  <meta name="Generator" content="Cocoa HTML Writer">
  <meta name="CocoaVersion" content="2575.5">
  <style type="text/css">
    p.p1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px Helvetica}
    p.p2 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px Helvetica; min-height: 14.0px}
  </style>
</head>
<body>
<p class="p1">&lt;!DOCTYPE html&gt;</p>
<p class="p2"><br></p>
<p class="p1">&lt;html&gt;</p>
<p class="p2"><br></p>
<p class="p1">&lt;head&gt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;meta charset="UTF-8"&gt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;title&gt;乒乓球动作要点小游戏&lt;/title&gt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;style&gt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>body { font-family: Arial, sans-serif; background: #f0f8ff; }</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>.container { max-width: 500px; margin: 30px auto; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 0 8px #ccc; }</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>.question { font-size: 20px; margin-bottom: 20px; }</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>.option { display: block; margin: 8px 0; padding: 10px; background: #e0f7fa; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>.option:hover { background: #b2ebf2; }</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>.result { font-size: 18px; margin-top: 20px; }</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>#nextBtn { margin-top: 16px; padding: 8px 16px; font-size: 16px; display: none; }</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;/style&gt;</p>
<p class="p2"><br></p>
<p class="p1">&lt;/head&gt;</p>
<p class="p2"><br></p>
<p class="p1">&lt;body&gt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;div class="container"&gt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>&lt;div class="question" id="question"&gt;&lt;/div&gt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>&lt;div id="options"&gt;&lt;/div&gt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>&lt;div class="result" id="result"&gt;&lt;/div&gt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>&lt;button id="nextBtn" onclick="nextQuestion()"&gt;下一题&lt;/button&gt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;/div&gt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;script&gt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// 题库</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>const quiz = [</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>{</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>question: "正手击球时，下列哪项是正确的动作要点？",</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>options: [</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                    </span>"挥拍时手腕要僵硬",</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                    </span>"迎球时身体要转动",</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                    </span>"脚站在一起不动"</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>],</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>answer: 1 // 索引从0开始</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>},</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>{</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>question: "乒乓球发球时，正确的动作要点是？",</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>options: [</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                    </span>"球要先抛起再击打",</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                    </span>"直接用球拍把球挑起来",</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                    </span>"用脚把球踢起来"</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>],</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>answer: 0</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>},</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>{</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>question: "乒乓球准备姿势时，应该：",</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>options: [</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                    </span>"两脚分开与肩同宽",</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                    </span>"双腿并拢站直",</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                    </span>"背对球台"</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>],</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>answer: 0</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>];</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>let current = 0;</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function showQuestion() {</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>document.getElementById('result').innerText = '';</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>document.getElementById('nextBtn').style.display = 'none';</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const q = quiz[current];</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>document.getElementById('question').innerText = (current + 1) + ". " + q.question;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const optionsDiv = document.getElementById('options');</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>optionsDiv.innerHTML = '';</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>q.options.forEach((opt, i) =&gt; {</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>const btn = document.createElement('button');</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>btn.className = 'option';</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>btn.innerText = opt;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>btn.onclick = () =&gt; checkAnswer(i);</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>optionsDiv.appendChild(btn);</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function checkAnswer(selected) {</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const q = quiz[current];</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const resultDiv = document.getElementById('result');</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (selected === q.answer) {</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>resultDiv.innerHTML = "答对了！";</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>resultDiv.style.color = "green";</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>} else {</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>resultDiv.innerHTML = "答错了，正确答案是：" + q.options[q.answer];</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>resultDiv.style.color = "red";</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// 禁用所有选项</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>document.querySelectorAll('.option').forEach(btn =&gt; btn.disabled = true);</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>document.getElementById('nextBtn').style.display = current &lt; quiz.length - 1 ? 'inline-block' : 'none';</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function nextQuestion() {</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>current++;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (current &lt; quiz.length) {</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>showQuestion();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// 初始化</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>showQuestion();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;/script&gt;</p>
<p class="p2"><br></p>
<p class="p1">&lt;/body&gt;</p>
<p class="p2"><br></p>
<p class="p1">&lt;/html&gt;</p>
</body>
</html>
