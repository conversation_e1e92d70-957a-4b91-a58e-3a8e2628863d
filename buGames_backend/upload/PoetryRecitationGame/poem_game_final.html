
<!DOCTYPE html>

<html>

<head>

    <meta charset="utf-8">

    <title>古诗文背诵游戏</title>

    <style>

        body {

            background: url('https://cdn.pixabay.com/photo/2016/11/21/00/41/mountains-1843159_1280.jpg') no-repeat center center fixed;

            background-size: cover;

            text-align: center;

            font-family: "Microsoft YaHei", Arial, sans-serif;

            color: #333;

        }

        #mainMenu {

            margin-top: 120px;

            background: rgba(255,255,255,0.88);

            display: inline-block;

            padding: 40px 60px;

            border-radius: 25px;

        }

        #studentSystem {

            background: rgba(255,255,255,0.93);

            display: inline-block;

            padding: 40px 60px;

            border-radius: 25px;

            margin-top: 30px;

        }

        button {

            font-size: 28px;

            margin: 30px 40px;

            padding: 24px 60px;

            border-radius: 20px;

            border: none;

            background: #4CAF50;

            color: white;

            cursor: pointer;

            box-shadow: 0 2px 8px rgba(44,62,80,0.13);

            transition: background 0.2s, transform 0.2s;

        }

        button:hover {

            background: #388E3C;

            transform: scale(1.04);

        }

        #studentListBox, #questionBankBox, #addPoemBox, #quizBox {

            display:none;

            position: fixed;

            left: 50%;

            top: 50%;

            transform: translate(-50%,-50%);

            background: #fff;

            border-radius: 20px;

            padding: 40px 30px;

            min-width:300px;

            box-shadow: 0 6px 28px rgba(0,0,0,0.23);

            z-index: 10;

            text-align: left;

            max-width:90vw; max-height:80vh; overflow:auto;

        }

        #studentList li {

            margin: 14px 0;

            font-size: 22px;

        }

        .btn-del {

            background: #f44336;

            margin-left: 15px;

            font-size: 18px;

            padding: 7px 18px;

            border-radius: 8px;

        }

        .btn-del:hover {

            background: #c62828;

        }

        label {

            font-size: 19px;

        }

        .input-long {

            font-size: 18px;

            width: 95%;

            padding: 6px 8px;

            border-radius: 7px;

        }

        textarea {

            font-size: 18px;

            width: 95%;

            border-radius: 7px;

            min-height: 66px;

            resize: vertical;

        }

        #quizQuestionNum {

            font-size: 20px;

            color: #245;

            margin-bottom: 10px;

        }

    </style>

</head>

<body>

<div id="mainMenu">

    <h1>古诗文背诵游戏</h1>

    <button onclick="showStudentSystem()">开始点名</button>

</div>

<div id="studentSystem" style="display:none;">

    <h2>学生点名系统</h2>

    <button onclick="showQuestionBank()">题库</button>

    <button onclick="callRandomStudent()">点名</button>

    <button onclick="showStudentList()">学生名单</button>

    <button onclick="showAddPoemBox()">添加新古诗</button>

    <button onclick="startPoemQuiz()">抽古诗出题</button>

    <div id="studentResult" style="margin:30px 0 10px 0; font-size:23px; color:#008855;"></div>

    <button onclick="backToMenu()">返回首页</button>

</div>

<!-- 学生名单弹窗 -->

<div id="studentListBox">

    <h3>学生名单</h3>

    <ul id="studentList"></ul>

    <input type="text" id="newStudent" placeholder="输入学生姓名" style="font-size:18px;padding:7px 10px;border-radius:8px;">

    <button onclick="addStudent()">添加学生</button>

    <br><br>

    <button onclick="closeStudentList()">关闭</button>

</div>

<!-- 题库弹窗 -->

<div id="questionBankBox">

    <h3>题库（点击诗名可展开内容）</h3>

    <div id="poemList"></div>

    <button onclick="closeQuestionBank()">关闭</button>

</div>

<!-- 添加新古诗弹窗 -->

<div id="addPoemBox">

    <h3>添加新古诗</h3>

    <label>题目：</label><br>

    <input type="text" id="addPoemTitle" class="input-long"><br>

    <label>作者：</label><br>

    <input type="text" id="addPoemAuthor" class="input-long"><br>

    <label>内容：（每句一行）</label><br>

    <textarea id="addPoemContent"></textarea><br>

    <button onclick="addPoem()">保存</button>

    <button onclick="closeAddPoemBox()">取消</button>

</div>

<!-- 抽题弹窗 -->

<div id="quizBox">

    <h3 id="quizPoemTitle">古诗出题</h3>

    <div id="quizQuestionNum"></div>

    <div id="quizQuestions"></div>

    <button id="nextQuizBtn" onclick="nextQuizQuestion()">下一题</button>

    <button style="display:none" id="quizCloseBtn" onclick="closeQuizBox()">关闭</button>

</div>

<script>

let students = ["张三", "李四", "王五"];

let poems = [

    {

        title: "四时田园杂兴（其二十五）",

        author: "宋·范成大",

        content: [

            "梅子金黄杏子肥，麦花雪白菜花稀。",

            "日长禽落无人过，惟有蜻蜓蛱蝶飞。"

        ]

    },

    {

        title: "清平乐・村居",

        author: "宋·辛弃疾",

        content: [

            "茅檐低小，溪上青青草。",

            "醉里吴音相媚好，白发谁家翁媪？",

            "大儿锄豆溪东，中儿正织鸡笼。",

            "最喜小儿亡赖，溪头卧剃莲蓬。"

        ]

    },

    {

        title: "宿新市徐公店",

        author: "宋·杨万里",

        content: [

            "篱落疏疏一径深，树头新绿未成阴。",

            "儿童急走追黄蝶，飞入菜花无处寻。"

        ]

    },

    {

        title: "卜算子•咏梅",

        author: "毛泽东",

        content: [

            "风雨送春归，飞雪迎春到。",

            "已是悬崖百丈冰，犹有花枝俏。",

            "俏也不争春，只把春来报。",

            "待到山花烂漫时，她在丛中笑。"

        ]

    },

    {

        title: "江畔独步寻花",

        author: "唐·杜甫",

        content: [

            "黄师塔前江水东，春光懒困倚微风。",

            "桃花一簇开无主，可爱深红爱浅红？"

        ]

    },

    {

        title: "蜂",

        author: "唐·罗隐",

        content: [

            "不论平地与山尖，无限风光尽被占。",

            "采得百花成蜜后，为谁辛苦为谁甜。"

        ]

    },

    {

        title: "独坐敬亭山",

        author: "唐·李白",

        content: [

            "众鸟高飞尽，孤云独去闲。",

            "相看两不厌，只有敬亭山。"

        ]

    },

    {

        title: "芙蓉楼送辛渐",

        author: "唐·王昌龄",

        content: [

            "寒雨连江夜入吴，平明送客楚山孤。",

            "洛阳亲友如相问，一片冰心在玉壶。"

        ]

    },

    {

        title: "塞下曲",

        author: "唐·卢纶",

        content: [

            "月黑雁飞高，单于夜遁逃。",

            "欲将轻骑逐，大雪满弓刀。"

        ]

    }

];

function showStudentSystem() {

    document.getElementById('mainMenu').style.display = 'none';

    document.getElementById('studentSystem').style.display = 'block';

}

function backToMenu() {

    document.getElementById('studentSystem').style.display = 'none';

    document.getElementById('mainMenu').style.display = 'block';

}

function showStudentList() {

    renderStudentList();

    document.getElementById('studentListBox').style.display = 'block';

}

function closeStudentList() {

    document.getElementById('studentListBox').style.display = 'none';

}

function addStudent() {

    let name = document.getElementById('newStudent').value.trim();

    if(name) {

        students.push(name);

        document.getElementById('newStudent').value = '';

        renderStudentList();

    }

}

function delStudent(idx) {

    students.splice(idx,1);

    renderStudentList();

}

function renderStudentList() {

    let ul = document.getElementById('studentList');

    ul.innerHTML = '';

    students.forEach((name, idx) => {

        ul.innerHTML += `<li>${name} <button class="btn-del" onclick="delStudent(${idx})">删除</button></li>`;

    });

}

function callRandomStudent() {

    if(students.length === 0) {

        document.getElementById('studentResult').innerHTML = '请先添加学生！';

        return;

    }

    let idx = Math.floor(Math.random() * students.length);

    let name = students[idx];

    document.getElementById('studentResult').innerHTML = `<strong>请 ${name} 同学背诵！</strong>`;

}

function showQuestionBank() {

    renderPoemList();

    document.getElementById('questionBankBox').style.display = 'block';

}

function closeQuestionBank() {

    document.getElementById('questionBankBox').style.display = 'none';

}

function renderPoemList() {

    let html = '';

    poems.forEach((poem, idx) => {

        html += `<div style="margin:10px 0;">

            <span style="font-size:20px; font-weight:bold; color:#205">${poem.title}</span>

            <span style="color:#333; font-size:16px;">（${poem.author}）</span>

            <a href="javascript:void(0)" onclick="togglePoemContent(${idx})" style="color:blue; margin-left:10px; font-size:16px;">[展开/收起]</a>

            <div id="poemContent${idx}" style="display:none; font-size:18px; margin-left:10px; color:#222;">${poem.content.join('<br>')}</div>

        </div>`;

    });

    document.getElementById('poemList').innerHTML = html;

}

function togglePoemContent(idx) {

    let div = document.getElementById('poemContent'+idx);

    div.style.display = div.style.display === "none" ? "block" : "none";

}

function showAddPoemBox() {

    document.getElementById('addPoemBox').style.display = 'block';

    document.getElementById('addPoemTitle').value = '';

    document.getElementById('addPoemAuthor').value = '';

    document.getElementById('addPoemContent').value = '';

}

function closeAddPoemBox() {

    document.getElementById('addPoemBox').style.display = 'none';

}

function addPoem() {

    let title = document.getElementById('addPoemTitle').value.trim();

    let author = document.getElementById('addPoemAuthor').value.trim();

    let content = document.getElementById('addPoemContent').value.trim().split('\n').filter(l=>l.length>0);

    if(title && author && content.length > 0) {

        poems.push({title, author, content});

        alert('古诗添加成功！');

        closeAddPoemBox();

        if(document.getElementById('questionBankBox').style.display === 'block'){

            renderPoemList();

        }

    } else {

        alert('请填写完整的古诗信息！');

    }

}

let quizQuestionsArr = [];

let quizIndex = 0;

function startPoemQuiz() {

    let poemIndices = [];

    let total = poems.length;

    let pick = Math.min(10, total);

    let indices = Array.from({length: total}, (v, k) => k);

    for (let i = total - 1; i > 0; i--) {

        let j = Math.floor(Math.random() * (i + 1));

        [indices[i], indices[j]] = [indices[j], indices[i]];

    }

    poemIndices = indices.slice(0, pick);

    quizQuestionsArr = [];

    for (let i = 0; i < pick; i++) {

        let poem = poems[poemIndices[i]];

        let qtype = "fill";

        if (i >= 3 && i < 6) qtype = "nextline";

        else if (i >= 6 && i < 8) qtype = "halfline";

        else if (i >= 8) qtype = "recite";

        quizQuestionsArr.push(genQuizQuestion(poem, qtype));

    }

    quizIndex = 0;

    showQuizQuestion();

    document.getElementById('quizBox').style.display = 'block';

}

function genQuizQuestion(poem, type) {

    let lines = poem.content;

    let lineIdx = Math.floor(Math.random() * lines.length);

    let line = lines[lineIdx];

    let question = "";

    if (type === "fill") {

        if (line.length < 2) return `请写出：“${line}”`;

        let randIdx = Math.floor(Math.random() * line.length);

        let blanked = line.substring(0, randIdx) + "____" + line.substring(randIdx + 1);

        question = `【填空题】${blanked} ——（${poem.title}·${poem.author}）`;

    } else if (type === "nextline") {

        let found = false;

        for (let i = 0; i < lines.length; i++) {

            let arr = lines[i].split('，');

            if (arr.length === 2) {

                question = `【补下句】${arr[0]}，____ ——（${poem.title}·${poem.author}）`;

                found = true;

                break;

            }

        }

        if (!found) {

            question = `【补下句】请写出“${line}”的下一句 ——（${poem.title}·${poem.author}）`;

        }

    } else if (type === "halfline") {

        let half = Math.floor(line.length / 2);

        question = `【补全诗句】${line.substring(0, half)}____ ——（${poem.title}·${poem.author}）`;

    } else if (type === "recite") {

        if (Math.random() > 0.5) {

            question = `【背诵全诗】请背诵《${poem.title}》`;

        } else {

            question = `【背诵全诗】请背诵《${poem.title}》（作者：${poem.author}）`;

        }

    }

    return question;

}

function showQuizQuestion() {

    let total = quizQuestionsArr.length;

    if (quizIndex < total) {

        document.getElementById('quizQuestionNum').innerHTML = "第 "+ (quizIndex+1) + " / " + total + " 题";

        document.getElementById('quizQuestions').innerHTML = quizQuestionsArr[quizIndex];

        document.getElementById('nextQuizBtn').style.display = 'inline-block';

        document.getElementById('quizCloseBtn').style.display = 'none';

    } else {

        document.getElementById('quizQuestionNum').innerHTML = "";

        document.getElementById('quizQuestions').innerHTML = "<b>全部答题完成！</b>";

        document.getElementById('nextQuizBtn').style.display = 'none';

        document.getElementById('quizCloseBtn').style.display = 'inline-block';

    }

}

function nextQuizQuestion() {

    quizIndex++;

    showQuizQuestion();

}

function closeQuizBox() {

    document.getElementById('quizBox').style.display = 'none';

}

</script>

</body>

</html>

