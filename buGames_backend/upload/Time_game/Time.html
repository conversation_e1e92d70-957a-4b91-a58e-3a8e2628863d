
<!DOCTYPE html>

<html>

<head>

    <title>Time Battle Quiz!</title>

    <style>

        body {

            font-family: 'Comic Sans MS', <PERSON>l, sans-serif;

            background: linear-gradient(120deg, #fcf6ba 0%, #f5d6e0 100%);

            padding: 20px;

        }

        .quiz-container {

            max-width: 500px;

            margin: auto;

            background: #ffffffbb;

            padding: 25px;

            border-radius: 20px;

            box-shadow: 0 8px 16px rgba(0,0,0,0.10);

            text-align: center;

        }

        .hearts {

            font-size: 28px;

            margin: 10px;

        }

        .monster {

            font-size: 50px;

        }

        .player {

            font-size: 40px;

        }

        .question {

            font-size: 22px;

            margin-bottom: 18px;

            color: #5344a0;

        }

        .option {

            display: block;

            width: 85%;

            margin: 10px auto;

            padding: 10px;

            background: #ffe7be;

            color: #5344a0;

            font-size: 18px;

            border: 2px solid #e7a977;

            border-radius: 8px;

            cursor: pointer;

            transition: 0.2s;

        }

        .option:hover {

            background: #f7cac9;

            color: #fff;

        }

        #feedback {

            font-weight: bold;

            margin-top: 15px;

            font-size: 18px;

            min-height: 40px;

        }

        #nextBtn {

            background: #a6d4fa;

            color: #333;

            padding: 10px 20px;

            font-size: 18px;

            border: none;

            border-radius: 8px;

            margin-top: 18px;

            cursor: pointer;

            box-shadow: 2px 2px 5px #888;

        }

        #nextBtn:hover {

            background: #2673b8;

            color: #fff;

        }

    </style>

</head>

<body>

    <div class="quiz-container">

        <div>

            <div class="monster">👾 Monster: <span id="monsterHearts" class="hearts"></span></div>

            <div class="player">😊 You: <span id="playerHearts" class="hearts"></span></div>

        </div>

        <h2>Time Battle Quiz! ⏰</h2>

        <div id="question" class="question"></div>

        <div id="options"></div>

        <div id="feedback"></div>

        <button id="nextBtn" onclick="nextQuestion()">Next</button>

    </div>

    <script>

        const questions = [

            {

                question: "How many minutes are in 1 hour?",

                options: ["30", "60", "100"],

                answer: 1,

                hint: "A clock goes all the way around in 60 minutes."

            },

            {

                question: "If it is 3:15, what time will it be in 30 minutes?",

                options: ["3:45", "4:15", "3:30"],

                answer: 0,

                hint: "Add 30 minutes to the minute hand."

            },

            {

                question: "How many seconds are in a minute?",

                options: ["60", "100", "30"],

                answer: 0,

                hint: "A clock counts up to 60 seconds before the next minute."

            }

        ];

        let currentQuestion = 0;

        let monsterLife = 3; // Monster's hearts

        let playerLife = 3;  // Player's hearts

        let answered = false;


        function updateHearts() {

            document.getElementById('monsterHearts').textContent = "❤️".repeat(monsterLife);

            document.getElementById('playerHearts').textContent = "❤️".repeat(playerLife);

        }


        function showQuestion() {

            answered = false;

            document.getElementById('feedback').textContent = '';

            let q = questions[currentQuestion];

            document.getElementById('question').textContent = q.question;

            let optionsDiv = document.getElementById('options');

            optionsDiv.innerHTML = '';

            q.options.forEach((opt, i) => {

                let btn = document.createElement('button');

                btn.textContent = opt;

                btn.className = 'option';

                btn.onclick = () => checkAnswer(i, btn);

                optionsDiv.appendChild(btn);

            });

            document.getElementById('nextBtn').disabled = true;

            updateHearts();

        }


        function checkAnswer(selectedIdx, btn) {

            if (answered) return;

            let q = questions[currentQuestion];

            let feedback = document.getElementById('feedback');

            if (selectedIdx === q.answer) {

                monsterLife--;

                feedback.innerHTML = "🎯 <span style='color:green'>You hit the monster! Great job!</span>";

                btn.style.background = "#baffc9";

                btn.style.color = "#245e2c";

                answered = true;

            } else {

                playerLife--;

                feedback.innerHTML = `😈 <span style='color:red'>Oops! The monster hit you! Hint: ${q.hint}</span>`;

                btn.style.background = "#f78ca2";

                btn.style.color = "#fff";

                answered = true;

            }

            document.querySelectorAll('.option').forEach(b => b.disabled = true);

            updateHearts();

            if (monsterLife === 0) {

                feedback.innerHTML = "🏆 <span style='color:green'>You defeated the monster! Well done!</span>";

                document.getElementById('options').innerHTML = '';

                document.getElementById('nextBtn').style.display = 'none';

                document.getElementById('question').textContent = "";

            } else if (playerLife === 0) {

                feedback.innerHTML = "💀 <span style='color:red'>The monster wins! Try again next time!</span>";

                document.getElementById('options').innerHTML = '';

                document.getElementById('nextBtn').style.display = 'none';

                document.getElementById('question').textContent = "";

            } else {

                document.getElementById('nextBtn').disabled = false;

            }

        }


        function nextQuestion() {

            if (monsterLife === 0 || playerLife === 0) return;

            currentQuestion++;

            if (currentQuestion < questions.length) {

                showQuestion();

            } else {

                // No more questions, decide winner by hearts

                let feedback = document.getElementById('feedback');

                document.getElementById('options').innerHTML = '';

                document.getElementById('nextBtn').style.display = 'none';

                document.getElementById('question').textContent = "";

                if (monsterLife < playerLife) {

                    feedback.innerHTML = "🏆 <span style='color:green'>You finished the quiz and defeated the monster!</span>";

                } else if (playerLife < monsterLife) {

                    feedback.innerHTML = "💀 <span style='color:red'>The monster wins! Try again next time!</span>";

                } else {

                    feedback.innerHTML = "🤝 It's a draw!";

                }

            }

        }


        // Initialize display

        showQuestion();

    </script>

</body>

</html>

